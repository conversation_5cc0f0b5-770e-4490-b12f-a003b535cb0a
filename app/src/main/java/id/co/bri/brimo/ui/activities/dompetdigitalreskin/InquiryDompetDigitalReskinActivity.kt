package id.co.bri.brimo.ui.activities.dompetdigitalreskin

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.graphics.Typeface
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.text.Editable
import android.text.InputType
import android.text.Spannable
import android.text.SpannableString
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.util.TypedValue
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.GridLayoutManager
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.dompetdigital.RekomendasiTopUpAdapterReskin
import id.co.bri.brimo.contract.IPresenter.dompetdigitalreskin.IInquiryDompetDigitalReskinPresenter
import id.co.bri.brimo.contract.IView.dompetdigitalreskin.IInquiryDompetDigitalReskinView
import id.co.bri.brimo.databinding.ActivityInquiryDompetDigitalReskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.BillingDetailOpen
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.EwalletProductResponse
import id.co.bri.brimo.models.apimodel.response.EwalletBalanceResponse
import id.co.bri.brimo.models.apimodel.response.SaldoReponse
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.InquiryDompetDigitalResponse
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.OptionAmountItem
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.customviews.switchbutton.SwitchView
import id.co.bri.brimo.ui.fragments.SumberDanaFragment
import id.co.bri.brimo.ui.fragments.SumberDanaFragmentRevamp
import id.co.bri.brimo.util.custom_numpad.CustomNumpadHelper
import id.co.bri.brimo.util.custom_numpad.NumpadType
import id.co.bri.brimo.util.attachNumpad
import java.math.BigInteger
import java.util.function.Consumer
import javax.inject.Inject
import androidx.core.graphics.toColorInt

class InquiryDompetDigitalReskinActivity : NewSkinBaseActivity(), IInquiryDompetDigitalReskinView,
    SumberDanaFragmentRevamp.SelectSumberDanaInterface,
    View.OnClickListener,
    SumberDanaFragment.SelectSumberDanaInterface, SwitchView.ISwitchListener {

    private lateinit var binding: ActivityInquiryDompetDigitalReskinBinding
    private lateinit var mbrivaOpenResponse: List<BillingDetailOpen>
    private lateinit var openModel: BillingDetailOpen
    private var mListFailed: List<Int>? = mutableListOf()
    private var mListAccountModel: List<AccountModel>? = null
    private var isClick = false
    private var model: AccountModel? = null
    private var saldo: Double = 0.0
    private var counter: Int = 0
    private var minTrx: Long = 0
    private var saveStr: String = ""
    private var saldoString: String = ""
    private var defaultAkun: String? = null
    private var minTrxString: String = ""
    private var feeAdminString: String = ""
    private var nominalStrClr: String = ""
    private var nominalString: String = ""
    private var saldoHold: Boolean = false
    private var hideCurrency = false

    private lateinit var adapter: RekomendasiTopUpAdapterReskin
    private lateinit var numpadHelper: CustomNumpadHelper

    @Inject
    lateinit var presenter: IInquiryDompetDigitalReskinPresenter<IInquiryDompetDigitalReskinView>

    companion object {

        private lateinit var mInquiryDompetRevampResponse: InquiryDompetDigitalResponse
        private var mUrlConfirm: String = ""
        private var mUrlPayment: String = ""
        private var mNominal = ""
        private var mPhoneNumber = ""
        private var isFromFastMenu: Boolean = false
        private var mSelectedWallet: EwalletProductResponse? = null
        private var mWalletBalance: EwalletBalanceResponse? = null

        private const val TAG_RESPONSE = "response"
        private const val TAG_PHONE_NUMBER = "tag_phone_number"
        private const val TAG_WALLET_INFO = "wallet_info"
        private const val TAG_WALLET_BALANCE = "wallet_balance"

        @JvmStatic
        @JvmOverloads
        fun launchIntent(
            caller: Activity,
            inquiryDompetDigitalResponse: InquiryDompetDigitalResponse,
            urlConfirm: String,
            urlPayment: String,
            fromFastMenu: Boolean,
            nominal: String,
            phoneNumber: String,
            selectedWallet: EwalletProductResponse? = null,
            walletBalance: EwalletBalanceResponse? = null
        ) {
            val intent = Intent(caller, InquiryDompetDigitalReskinActivity::class.java)
            intent.putExtra(TAG_RESPONSE, Gson().toJson(inquiryDompetDigitalResponse))
            intent.putExtra(TAG_WALLET_INFO, Gson().toJson(selectedWallet))
            intent.putExtra(TAG_PHONE_NUMBER, phoneNumber)
            if (walletBalance != null) {
                intent.putExtra(TAG_WALLET_BALANCE, Gson().toJson(walletBalance))
            }
            mUrlConfirm = urlConfirm
            mUrlPayment = urlPayment
            isFromFastMenu = fromFastMenu
            mNominal = nominal
            mPhoneNumber = phoneNumber
            mSelectedWallet = selectedWallet
            mWalletBalance = walletBalance
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityInquiryDompetDigitalReskinBinding.inflate(layoutInflater)
        setContentView(binding.root)

        if (intent.extras != null) {
            parseIntent()
        }

        injectDependency()
        setupView()
    }

    private fun parseIntent() {
        if (intent.extras != null) {
            if (intent.hasExtra(TAG_RESPONSE)) {
                mInquiryDompetRevampResponse = Gson().fromJson(
                    intent.extras?.getString(TAG_RESPONSE),
                    InquiryDompetDigitalResponse::class.java
                )
            }

            if (intent.hasExtra(TAG_WALLET_INFO)) {
                mSelectedWallet = Gson().fromJson(
                    intent.extras?.getString(TAG_WALLET_INFO),
                    EwalletProductResponse::class.java
                )
            }

            if (intent.hasExtra(TAG_PHONE_NUMBER)) {
                mPhoneNumber = intent?.extras?.getString(TAG_PHONE_NUMBER, "") ?: ""
            }

            if (intent.hasExtra(TAG_WALLET_BALANCE)) {
                mWalletBalance = Gson().fromJson(
                    intent.extras?.getString(TAG_WALLET_BALANCE),
                    EwalletBalanceResponse::class.java
                )
            }
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlConfirm(mUrlConfirm)
        presenter.start()
    }

//    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
//        val view = currentFocus
//
//        if (ev.action == MotionEvent.ACTION_DOWN && view is EditText) {
//            val outRect = Rect()
//            view.getGlobalVisibleRect(outRect)
//
//            val tappedOutsideEditText = !outRect.contains(ev.rawX.toInt(), ev.rawY.toInt())
//            val tappedOutsideNumpad = !numpadHelper.isTouchInsideNumpad(ev)
//
//            // ❗ hanya hide jika klik di luar EditText DAN di luar numpad
//            if (tappedOutsideEditText && tappedOutsideNumpad) {
//                view.clearFocus()
//                numpadHelper.hideKeyboard()
//            }
//        }
//
//        return super.dispatchTouchEvent(ev)
//    }

    private fun setupView() {
        if (isFromFastMenu) {
            binding.tvNominalAccount.visibility = View.GONE
            binding.tvNumberAccount.apply {
                setTypeface(typeface, Typeface.BOLD) // Keeps current font family, adds bold
                setTextColor(ContextCompat.getColor(context, R.color.black_ns_main))
                setTextSize(TypedValue.COMPLEX_UNIT_SP, 14f)
            }
        }

        // Setup show/hide currency functionality
        binding.ivShowhideCurrency.visibility = if(isFromFastMenu) View.GONE else View.VISIBLE

        binding.ivShowhideCurrency.setOnClickListener {
            hideCurrency = hideCurrency.toggle()
            binding.ivShowhideCurrency.setImageResource(if(!hideCurrency) R.drawable.icon_unhide_eye else R.drawable.icon_hide_eye)

            // Update displays based on current state
            model?.let { accountModel ->
                // Update account number display
                binding.tvNumberAccount.text = if(hideCurrency) maskAccountNumber(accountModel.acoountString) else accountModel.acoountString

                // Update balance display
                if (accountModel.saldoReponse != null) {
                    // If we have saldo response, use the full onChangeSourcePayment logic
                    onChangeSourcePayment(accountModel)
                } else {
                    // If no saldo response, update balance display based on current saldo and saldoString
                    if(hideCurrency) {
                        binding.tvNominalAccount.text = maskAmount(saldo)
                    } else {
                        // Use the existing balance display logic from setupAccount
                        if (accountModel.acoount == defaultAkun && saldoString != "-") {
                            binding.tvNominalAccount.text = GeneralHelper.formatNominalIDR(accountModel.currency, saldoString)
                        } else {
                            binding.tvNominalAccount.text = "Rp." + GeneralHelper.formatNominal(saldo)
                        }
                    }
                }
            }
        }

        GeneralHelperNewSkin.setToolbar(
            this, binding.toolbar.toolbar,
            GeneralHelper.getString(R.string.ewallet)
        )

        val trimmedNumber = mPhoneNumber.replaceFirst(
            "^0+(?=\\d)".toRegex(),
            GeneralHelper.getString(R.string.hint_prefix_62)
        )

        // Create SpannableString to apply different colors to different parts
        val spannablePhoneNumber = SpannableString(trimmedNumber)
        val prefix = GeneralHelper.getString(R.string.hint_prefix_62)

        if (trimmedNumber.startsWith(prefix)) {
            // Apply disabled color to the "+62" prefix
            spannablePhoneNumber.setSpan(
                ForegroundColorSpan(ContextCompat.getColor(this, R.color.text_disabled_default_ns)),
                0,
                prefix.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )

            // Apply black color to the rest of the number
            spannablePhoneNumber.setSpan(
                ForegroundColorSpan(ContextCompat.getColor(this, R.color.text_black_default_ns)),
                prefix.length,
                trimmedNumber.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        binding.etNoPelanggan.setText(spannablePhoneNumber)
        binding.tvHint.setText(GeneralHelper.getString(R.string.txt_nomor_hp))

        binding.etNoPelanggan.isEnabled = false

        //set nominal jika hit inquiry dari history
        if (mNominal != "") {
            // Set only the numeric value, prefixText will handle "Rp"
            val formattedNominal = GeneralHelper.formatNominalBiasa(mNominal.toDouble())
            nominalString = mNominal
            binding.etNominal.setText(formattedNominal)
        } else {
            binding.etNominal.setText("")
        }

        binding.etNote.apply {
            setInputType(InputType.TYPE_CLASS_TEXT)
        }

        model = AccountModel()

        mListAccountModel = mInquiryDompetRevampResponse.accountModel
        mbrivaOpenResponse = mInquiryDompetRevampResponse.billingDetailOpen
        feeAdminString = mInquiryDompetRevampResponse.adminFee.toString()

        for (billingDetailOpen in mbrivaOpenResponse)
            openModel = billingDetailOpen

        // Load wallet icon - use selected wallet info if available, otherwise use openModel
        if (mSelectedWallet != null) {
            // Load icon from selected connected wallet
            GeneralHelper.loadIconTransaction(
                this@InquiryDompetDigitalReskinActivity,
                mSelectedWallet!!.iconPath,
                mSelectedWallet!!.iconName?.split(".")?.get(0) ?: "",
                binding.ivWalletIcon,
                GeneralHelper.getImageId(this, "ic_menu_qna_dompet_digital")
            )

            // Set wallet balance if available
            if (mWalletBalance != null) {
                binding.tvBalanceWallet.text = mWalletBalance!!.value_string ?: "Rp 0"
                binding.tvBalanceWallet.visibility = View.VISIBLE
            } else {
                binding.tvBalanceWallet.text = "Rp 0"
                binding.tvBalanceWallet.visibility = View.VISIBLE
            }
        } else {
            // Load icon from inquiry response (original behavior)
            GeneralHelper.loadIconTransaction(
                this@InquiryDompetDigitalReskinActivity,
                openModel.iconPath,
                openModel.iconName?.split("\\.")?.get(0) ?: "",
                binding.ivWalletIcon,
                0
            )

            // Hide balance for regular inquiry
            binding.tvBalanceWallet.visibility = View.GONE
        }

        binding.iconContainer.visibility = View.VISIBLE

        for (accountModel in mListAccountModel!!) {
            if (accountModel.isDefault == 1) {
                model = accountModel
                break
            } else {
                model = mListAccountModel!![0]
            }
        }

        //set minimum
        if (mInquiryDompetRevampResponse.minimumTransactionString.isNotEmpty()) {
            minTrx = mInquiryDompetRevampResponse.minimumTransaction
            minTrxString = mInquiryDompetRevampResponse.minimumTransactionString
        }

        binding.helperText.setText(
            String.format(
                GeneralHelper.getString(R.string.minimal_rp),
                minTrxString
            )
        )

        if (model?.acoountString != null) {
            binding.tvNumberAccount.text = if(hideCurrency) maskAccountNumber(model!!.acoountString) else model!!.acoountString
        } else {
            binding.tvNumberAccount.text = "-"
        }

        saveStr = mInquiryDompetRevampResponse.nameDefault

        adapter = RekomendasiTopUpAdapterReskin(
            onItemClick = { selectedItem ->
                // Set only the numeric value, prefixText will handle "Rp"
                val formattedAmount = GeneralHelper.formatNominalBiasa(selectedItem.value.toDouble())
                nominalString = selectedItem.name
                binding.etNominal.setText(formattedAmount)
                binding.etNominal.requestFocus()
                binding.etNominal.setSelection(binding.etNominal.text.length)
                checkButton()
                checkWarningCurrency()
            },
            items = mInquiryDompetRevampResponse.optionAmount
        )
        val layoutManager = GridLayoutManager(
            applicationContext, 2
        )
        binding.rvOptionAmount.layoutManager = layoutManager
        binding.rvOptionAmount.itemAnimator = DefaultItemAnimator()
        binding.rvOptionAmount.adapter = adapter

        initiateViews()

        // Trigger adapter selection and button check if nominal was set from history
        if (mNominal != "") {
            // Post to ensure text watchers are set up first
            binding.etNominal.post {
                updateAdapterSelection()
            }
        }

    }

    private fun initiateViews() {
        // Create a unified text watcher that handles both formatting and selection
        val unifiedTextWatcher = object : TextWatcher {
            private var isUpdating = false

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

            override fun afterTextChanged(s: Editable?) {
                if (isUpdating) return

                isUpdating = true
                try {
                    // Format the numeric text with dots (e.g., 10000 -> 10.000)
                    val currentText = s.toString()
                    val cleanNumber = currentText.replace(".", "").replace(",", "").trim()

                    if (cleanNumber.isNotEmpty() && cleanNumber.all { it.isDigit() }) {
                        val formattedText = GeneralHelper.formatNominalBiasa(cleanNumber.toDouble())
                        if (currentText != formattedText) {
                            binding.etNominal.setText(formattedText)
                            binding.etNominal.setSelection(formattedText.length)
                        }
                    }

                    // Show/hide clear button based on content
                    binding.ivClearNominal.visibility = if (currentText.isNotEmpty()) View.VISIBLE else View.GONE

                    // Update adapter selection and button state
                    updateAdapterSelection()
                } finally {
                    isUpdating = false
                }
            }
        }

        binding.etNominal.addTextChangedListener(unifiedTextWatcher)

        // Handle clear button click
        binding.ivClearNominal.setOnClickListener {
            binding.etNominal.setText("")
            binding.etNominal.requestFocus()
        }

        binding.accountDetail.setOnClickListener(this)
        binding.btnSubmit.setOnClickListener(this)
    }

    override fun afterText(editable: Editable?) {
        // This method is called by the base class activityTextListener
        // We don't use it anymore since we have unified text watcher
        updateAdapterSelection()
    }

    private fun updateAdapterSelection() {
        clearSelected()

        val normalizedInput = getNormalizedAmount() // still a String like "50000"

        val index = mInquiryDompetRevampResponse.optionAmount.indexOfFirst {
            it.value.toString() == normalizedInput
        }

        mInquiryDompetRevampResponse.optionAmount.forEachIndexed { i, item ->
            item.setSelected(i == index)
        }

        adapter.setSelectedPosition(index)
        adapter.notifyDataSetChanged()
        checkButton()
        checkWarningCurrency()
    }

    override fun setDefaultSaldo(
        saldoPref: Double,
        saldoStringPref: String,
        accountPref: String,
        saldoHoldPref: Boolean
    ) {
        // load default saldo dari preference
        this.saldoString = saldoStringPref
        this.saldo = saldoPref
        defaultAkun = accountPref
        saldoHold = saldoHoldPref

        //set layout
        setupAccount(saldoPref)
    }

    override fun onGetDataConfirmation(generalConfirmationResponse: GeneralConfirmationResponse) {
        // Do nothing
    }

    override fun onExceptionTrxExpired(desc: String) {
        val returnIntent = Intent()
        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, desc)
        this.setResult(RESULT_CANCELED, returnIntent)
        finish()
    }

    private fun setupAccount(saldoDefault: Double) {
        //List Account
        model = AccountModel()
        if (mInquiryDompetRevampResponse.accountModel.size > 0)
            mListAccountModel = mInquiryDompetRevampResponse.accountModel

        for (accountModel in mListAccountModel!!) {
            if (accountModel.isDefault == 1) {
                model = accountModel
                break
            } else {
                model = mListAccountModel?.get(0)
            }
        }

        //jika get minimum tidak null
        saldo = if (model?.minimumBalance != null) {
            saldoDefault - model?.minimumBalance!!
        } else {
            saldoDefault
        }

        if (model?.acoount != null) {
            val saldoText: String = saldoDefault.toString()
            if (saldoText != "") {
                saldo = saldoDefault
                // Check if we should use the preference saldo string or the default saldo
                if(hideCurrency) {
                    binding.tvNominalAccount.text = maskAmount(saldoDefault)
                } else {
                    if (model?.acoount == defaultAkun && saldoString != "-") {
                        binding.tvNominalAccount.text =
                            GeneralHelper.formatNominalIDR(model?.currency, saldoString)
                    } else {
                        binding.tvNominalAccount.text = "Rp." + GeneralHelper.formatNominal(saldoDefault)
                    }
                }
            } else {
                binding.tvNominalAccount.text = "-"
            }
        }
        checkButton()
    }

    private fun checkButton() {
        val rawNominal = binding.etNominal.text.toString()
        // Remove dots for validation (no need to remove "Rp" since it's in prefixText)
        nominalStrClr = if (rawNominal.isEmpty()) "-"
        else rawNominal.replace(".", "").trim()

        val nominal = nominalStrClr.toLongOrNull()

        // Invalid input or empty
        if (rawNominal.isEmpty() || nominal == null || nominal == 0L) {
            disableButton(true)
            return
        }

        val minRequired = BigInteger.valueOf(minTrx)
        val currentNominal = BigInteger.valueOf(nominal)
        val maxAllowed = BigInteger.valueOf(saldo.toLong() - getMinBalance(model!!))

        binding.helperText.apply {
            when {
                currentNominal < minRequired -> {
                    setText(
                        String.format(
                            GeneralHelper.getString(R.string.text_minimal_top_up),
                            minTrxString
                        )
                    )
                    setTextColor(
                        ContextCompat.getColor(
                            this@InquiryDompetDigitalReskinActivity,
                            R.color.red_ns_main
                        )
                    )
                }

                else -> {
                    setText(
                        String.format(
                            GeneralHelper.getString(R.string.minimal_topup),
                            minTrxString
                        )
                    )
                    setTextColor(
                        ContextCompat.getColor(
                            this@InquiryDompetDigitalReskinActivity,
                            R.color.text_gray_default_ns
                        )
                    )
                }
            }
        }

        when {
            currentNominal < minRequired -> disableButton(true)
            currentNominal > maxAllowed -> disableButton(!isFromFastMenu)
            else -> disableButton(false)
        }
    }

    private fun checkWarningCurrency() {
        model?.let { accountModel ->
            val payAmount = if (nominalStrClr.isNotEmpty() && nominalStrClr != "-") {
                nominalStrClr.toLongOrNull() ?: 0L
            } else {
                0L
            }

            // Calculate maxAllowed the same way as checkButton()
            val maxAllowed = BigInteger.valueOf(saldo.toLong() - getMinBalance(accountModel))
            val currentNominal = BigInteger.valueOf(payAmount)

            // Check if amount exceeds available balance (considering minimum balance)
            val isExceedingBalance = currentNominal > maxAllowed

            binding.tvWarningCurrency.apply {
                visibility = if(isExceedingBalance) View.VISIBLE else View.GONE
                fadeIn()
            }
            binding.tvNominalAccount.apply {
                setTextColor(if(isExceedingBalance) "#E84040".toColorInt() else "#181C21".toColorInt())
                fadeIn()
            }
        }
    }

    private fun disableButton(disable: Boolean) {
        if (disable) {
            binding.btnSubmit.isEnabled = false
            binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))
        } else {
            binding.btnSubmit.isEnabled = true
            binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light10))
        }
    }


    override fun onSelectSumberDana(bankModel: AccountModel) {
//        if (bankModel != null) {
//            model = bankModel
//        }
//
//        saldo = if (bankModel?.saldoReponse != null) {
//            val saldoStr = model?.saldoReponse?.balanceString.toString()
//            binding.tvNominalAccount.text =
//                GeneralHelper.formatNominalIDR(model?.currency, saldoStr)
//            if (bankModel.saldoReponse.balance != null) {
//                bankModel.saldoReponse.balance
//            } else {
//                binding.tvNominalAccount.text = String.format("%s%s", bankModel.currency, "-")
//                0.0
//            }
//        } else {
//            binding.tvNominalAccount.text = String.format("%s%s", bankModel?.currency, "-")
//            0.0
//        }
        onChangeSourcePayment(bankModel)

        checkButton()

    }

    private fun onChangeSourcePayment(bankModel: AccountModel) {
        binding.tvNumberAccount.text = if(hideCurrency) maskAccountNumber(bankModel.acoountString) else bankModel.acoountString
        model = bankModel
        saldo = bankModel.saldoReponse?.balance ?: 0.0

        GeneralHelper.loadIconTransaction(
            this,
            bankModel.imagePath,
            bankModel.imageName,
            binding.ivRekening,
            R.drawable.img_card_bg)

        if(bankModel.saldoReponse != null) {
            val saldoReponse: SaldoReponse = bankModel.saldoReponse

            if (saldoReponse.getBalanceString().equals("TO", ignoreCase = true)) {
                binding.tvNominalAccount.text = GeneralHelper.getString(R.string.gagal_memuat)
            } else if (saldoReponse.getBalanceString().equals("12", ignoreCase = true)) {
                binding.tvNominalAccount.text = GeneralHelper.getString(R.string.empty)
            } else if (saldoReponse.balanceString.equals("05", ignoreCase = true)) {
                binding.tvNominalAccount.text = GeneralHelper.getString(R.string.empty)
            } else {
                val currency = saldoReponse.currency
                isClick = true

                val nominalIdr = GeneralHelper.formatNominalIDR(
                    saldoReponse.currency,
                    saldoReponse.balanceString
                )

                // Check warning currency condition
                checkWarningCurrency()

                if(hideCurrency) {
                    binding.tvNominalAccount.text = maskAmount(saldoReponse.balance ?: 0.0)
                } else {
                    binding.tvNominalAccount.text = if (saldoReponse.signedBalanceString.isNotEmpty()
                    ) saldoReponse.signedBalanceString else if (currency != null) nominalIdr.substring(
                        0,
                        nominalIdr.length - 3
                    ) else saldoReponse.balanceString
                }
            }
        }
    }

    override fun onSendFailedList(list: MutableList<Int>?) {
        mListFailed = list
    }

    override fun onClick(v: View) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return
        }
        mLastClickTime = SystemClock.elapsedRealtime()
        when (v.id) {
            binding.accountDetail.id -> {
                gantiSumberDana()
            }

            binding.btnSubmit.id -> {
                ConfirmationDompetDigitalReskinActivity.launchIntent(
                    this,
                    openModel,
                    mInquiryDompetRevampResponse.saved,
                    mInquiryDompetRevampResponse.referenceNumber,
                    model!!,
                    binding.etNote.getText(),
                    nominalStrClr,
                    nominalString,
                    mInquiryDompetRevampResponse.adminFee.toString(),
                    mInquiryDompetRevampResponse.adminFeeString,
                    mUrlConfirm,
                    mUrlPayment,
                    isFromFastMenu
                )
            }
        }
    }

    private fun gantiSumberDana() {
        counter++
        if (mListAccountModel == null) {
            GeneralHelper.showToast(this, GeneralHelper.getString(R.string.no_account_list))
        } else {
            val selectedIndex = mListAccountModel?.indexOfFirst {
                model?.acoountString.equals(it.acoountString)
            }
            val rawNominal = binding.etNominal.text.toString()
            val nominalStr = if (rawNominal.isEmpty()) "0"
            else rawNominal.replace(".", "").trim()
            val nominalInt = nominalStr.toInt()

            val fragmentSumberDana =
                SumberDanaFragment(mListAccountModel, this, counter, mListFailed, selectedIndex!!, nominalInt,
                    isFromFastMenu
                )
            fragmentSumberDana.show(supportFragmentManager, Constant.TAG_PICK_ACCOUNT)


//            if (isFromFastMenu) {
//                val fragmentFmSumberDanaNew =
//                    SumberDanaFmFragmentRevamp(
//                        mListAccountModel,
//                        counter,
//                        nominalLong,
//                        this,
//                        isFromFastMenu,
//                        GeneralHelper.getString(R.string.e_wallet_change_source_of_funds)
//                    )
//                fragmentFmSumberDanaNew.show(supportFragmentManager, Constant.TAG_PICK_ACCOUNT)
//            } else {
//                val fragmentSumberDanaNew = SumberDanaFragmentRevamp(
//                    mListAccountModel,
//                    counter,
//                    nominalLong,
//                    this,
//                    mListFailed,
//                    isFromFastMenu,
//                    GeneralHelper.getString(R.string.e_wallet_change_source_of_funds)
//                )
//                fragmentSumberDanaNew.show(supportFragmentManager, Constant.TAG_PICK_ACCOUNT)
//            }
        }
    }

    @Deprecated("Deprecated in Java")
    @Suppress("DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
                finish()
            } else {
                if (data != null) {
                    this.setResult(RESULT_CANCELED, data)
                    finish()
                }
            }
        }
    }


    @SuppressLint("NotifyDataSetChanged")
    fun clearSelected() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            mInquiryDompetRevampResponse.optionAmount.forEach(Consumer { p: OptionAmountItem ->
                p.isSelected = false
            })
        } else {
            for (p in mInquiryDompetRevampResponse.optionAmount) {
                p.isSelected = false
            }
        }
        adapter.notifyDataSetChanged()
    }

    private fun getNormalizedAmount(): String {
        val raw = binding.etNominal.text.toString()
        return raw.replace(".", "")
            .replace(",", "")
            .trim()
    }

    private fun getMinBalance(model: AccountModel): Long {
        val tempAccountModel =
            mListAccountModel?.firstOrNull { it.acoount == model.acoount } ?: AccountModel()
        return when {
            tempAccountModel.minimumBalance != null && tempAccountModel.minimumBalance >= 0 -> tempAccountModel.minimumBalance.toLong()
            model.minimumBalance != null && model.minimumBalance >= 0 -> model.minimumBalance.toLong()
            else -> 0L
        }
    }

    override fun onSwitchChange(isChecked: Boolean) {
        // Do nothing
    }

    fun maskAccountNumber(account: String): String {
        return if (account.length >= 11) {
            val first4 = account.substring(0, 4)
            val last3 = account.takeLast(3)
            "$first4 **** **** $last3"
        } else {
            "**** **** ****"
        }
    }

    fun maskAmount(amount: Double): String {
        return "Rp" + "•".repeat(8)
    }
}

// Fade In extension function
fun View.fadeIn(duration: Long = 300) {
    this.apply {
        alpha = 0f
        animate()
            .alpha(1f)
            .setDuration(duration)
            .setListener(null)
    }
}

fun Boolean.toggle() = !this