package id.co.bri.brimo.contract.IPresenter.simpedes;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;

public interface IInfoSimpedesPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrlFormInfo(String urlDetail);

    void setUrlAddImpian(String urlAddImpian);

    void setUrlAddBrifine(String urlAddBrifine);

    void setUrlBrifineCheck(String urlBrifineCheck);

    void setUrlProductAmkkm(String urlProductAmkkm);

    void getImpian(String sAccount);

    void getAddImpian(String sAccount);

    void getAddBrifine(String sAccount);

    void getBrifineCheck(String sAccount);

    void getProductAmkkm(String sAccount);
}