package id.co.bri.brimo.contract.IPresenter.property

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IPresenter.base.IBaseFormRevampPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.base.IBaseFormRevampView
import id.co.bri.brimo.models.apimodel.request.InquiryPlnRequest
import id.co.bri.brimo.models.apimodel.request.property.InquiryPropertyRequest

interface IInputBillingPropertyPresenter<V : IMvpView> : IMvpPresenter<V>  {
    fun getDataInquiry(request: InquiryPropertyRequest)
    fun setInquiryUrl(url: String)
}