package id.co.bri.brimo.presenters.listrikrevamp.reskin

import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.base.InquiryConfirmation
import id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin.IFormListrikReskinPresenter
import id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin.SavedListNs
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.listrikrevamp.reskin.ICetakTokenReskinView
import id.co.bri.brimo.contract.IView.listrikrevamp.reskin.IFormListrikReskinView
import id.co.bri.brimo.contract.IView.listrikrevamp.reskin.ISavedListrikReskinView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.api.observer.ApiObserverKonfirmasi
import id.co.bri.brimo.data.api.observer.ApiReskinObserver
import id.co.bri.brimo.data.api.observer.ResExceptionErr
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.CetakTokenRequest
import id.co.bri.brimo.models.apimodel.request.ConfirmationRequest
import id.co.bri.brimo.models.apimodel.request.FastConfirmationRequest
import id.co.bri.brimo.models.apimodel.request.InquiryPlnRequest
import id.co.bri.brimo.models.apimodel.request.revampbriva.PayBrivaRevampRequest
import id.co.bri.brimo.models.apimodel.response.DataPlnResponse
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.InboxResponse
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.FormPulsaDataResponse
import id.co.bri.brimo.presenters.MvpPresenter
import id.co.bri.brimo.util.subscribeWithObserver
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit
import javax.inject.Inject

open class FormListrikReskinPresenter<V: IMvpView> @Inject constructor(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IFormListrikReskinPresenter<V> {

    protected var confirmationRequest: Any? = null

    override fun getDataForm() {
        getView().showProgress()

        if(getView() is IFormListrikReskinView){
            if (!isViewAttached) return

            val seqNum = brImoPrefRepository.seqNumber

            apiSource.getDataForm(GeneralHelper.getString(R.string.url_form_pln_v3), seqNum).subscribeWithObserver(
                compositeDisposable = compositeDisposable,
                schedulerProvider = schedulerProvider,
                createObserver = {
                    object : ApiObserver(getView(), seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView()?.onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()

                            if (response.data != null){
                                val formPln = response.getData(
                                    DataPlnResponse::class.java
                                )
                                (getView() as IFormListrikReskinView).onSuccessGetForm(formPln)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()

                            if (restResponse.code == RestResponse.ResponseCodeEnum.RC_FO.value) {
                                val response = restResponse.getData(EmptyStateResponse::class.java)
                                getView().onExceptionFO(response)
                            } else {
                                onApiError(restResponse)
                            }
                        }
                    }
                }
            )
        }
    }

    override fun postInquiry(request: InquiryPlnRequest) {
        getView().showProgress()

        if (!isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        apiSource.getData(GeneralHelper.getString(R.string.url_inquiry_pln_v3), request, seqNum).subscribeWithObserver(
            compositeDisposable = compositeDisposable,
            schedulerProvider = schedulerProvider,
            createObserver = {
                object : ApiReskinObserver(getView(), seqNum) {
                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val data = response.getData(
                            InquiryBrivaRevampResponse::class.java
                        )

                        (getView() as IFormListrikReskinView).onSuccessInquiry(data)
                    }

                    override fun onApiCallError(errRes: ResExceptionErr) {
                        if(errRes.code == Constant.RE12){
                            if(errRes.desc.contains(Constant.Electic.WRONG_IDPEL)) {
                                (getView() as IFormListrikReskinView).onExceptionWrongIDPel()
                            } else if(errRes.desc.contains(Constant.Electic.NOT_AVAIL) || errRes.desc.contains(Constant.Electic.DONE_PAYMENT)) {
                                (getView() as IFormListrikReskinView).onExceptionNotAvail(errRes.desc)
                            } else {
                                view.onException(Constant.TRANSAKSI_GAGAL)
                            }
                        }
                    }
                }
            }
        )
    }

    override fun getDataConfirmation(param: InquiryConfirmation) {
        getView().showProgress()
        val urlConfirmation = GeneralHelper.getString(R.string.url_confirmation_pln_v3)
        val fromFastMenu = false

        if (isViewAttached && urlConfirmation != null) {
            val seqNum = brImoPrefRepository.seqNumber

            confirmationRequest = if (fromFastMenu) FastConfirmationRequest(getFastMenuRequest(), param.refNum, param.accountNum, param.amount, param.save)
            else ConfirmationRequest(param.refNum, param.accountNum, param.amount, param.save)

            apiSource.getData(urlConfirmation, confirmationRequest, seqNum).subscribeWithObserver(
                compositeDisposable = compositeDisposable,
                schedulerProvider = schedulerProvider,
                createObserver = {
                    object : ApiReskinObserver(view, seqNum) {
                        override fun onApiCallSuccess(response: RestResponse) {
                            val brivaResponse = response.getData(
                                GeneralConfirmationResponse::class.java
                            )
                            (getView() as IFormListrikReskinView).onSuccessGetConfirmation(brivaResponse)
                        }

                        override fun onApiCallError(errRes: ResExceptionErr) {
                            //
                        }
                    }
                }
            )
        }
    }

    override fun payment(param: PayBrivaRevampRequest) {
        getView().showProgress()
        val mUrlPay = GeneralHelper.getString(R.string.url_payment_pln_v3)

        if (isViewAttached && mUrlPay != null) {
            val seqNum = brImoPrefRepository.seqNumber

            apiSource.getData(mUrlPay, param, seqNum).subscribeWithObserver(
                compositeDisposable = compositeDisposable,
                schedulerProvider = schedulerProvider,
                createObserver = {
                    object: ApiReskinObserver(view, seqNum) {
                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val receiptRevampResponse =
                                response.getData(ReceiptRevampResponse::class.java)

                            (getView() as IFormListrikReskinView).onSuccessGetPayment(receiptRevampResponse)
                        }

                        override fun onApiCallError(errRes: ResExceptionErr) {
                            //
                        }
                    }
                }
            )
        }
    }

    override fun cetakToken() {
        getView().showProgress()
        val mUrl = GeneralHelper.getString(R.string.cetak_token_pln)

        if (isViewAttached && mUrl != null) {
            val seqNum = brImoPrefRepository.seqNumber
            apiSource.getData(mUrl, CetakTokenRequest("0"), seqNum).subscribeWithObserver(
                compositeDisposable = compositeDisposable,
                schedulerProvider = schedulerProvider,
                createObserver = {
                    object: ApiObserverKonfirmasi(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String?) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val receiptRevampResponse =
                                response.getData(InboxResponse::class.java)

                            (getView() as ICetakTokenReskinView).onSuccessGetCetakToken(receiptRevampResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    }
                }
            )
        }
    }

    override fun addSavedList(param: SavedListNs) {
        getView().showProgress()
        val mUrl = GeneralHelper.getString(R.string.url_inquiry_add_saved_list_listrik)

        if (isViewAttached && mUrl != null) {
            val seqNum = brImoPrefRepository.seqNumber
            apiSource.getData(mUrl, param, seqNum).subscribeWithObserver(
                compositeDisposable = compositeDisposable,
                schedulerProvider = schedulerProvider,
                createObserver = {
                    object: ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String?) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            (getView() as ISavedListrikReskinView).onSuccess(response)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    }
                }
            )
        }
    }

    override fun updateSavedList(param: SavedListNs) {
        getView().showProgress()
        val mUrl = GeneralHelper.getString(R.string.url_update_pln_v3)

        if (isViewAttached && mUrl != null) {
            val seqNum = brImoPrefRepository.seqNumber
            apiSource.getData(mUrl, param, seqNum).subscribeWithObserver(
                compositeDisposable = compositeDisposable,
                schedulerProvider = schedulerProvider,
                createObserver = {
                    object: ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String?) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            (getView() as ISavedListrikReskinView).onSuccess(response)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    }
                }
            )
        }
    }

    override fun removeSavedList(param: SavedListNs) {
        getView().showProgress()
        val mUrl = GeneralHelper.getString(R.string.url_delete_pln_v3)

        if (isViewAttached && mUrl != null) {
            val seqNum = brImoPrefRepository.seqNumber
            apiSource.getData(mUrl, param, seqNum).subscribeWithObserver(
                compositeDisposable = compositeDisposable,
                schedulerProvider = schedulerProvider,
                createObserver = {
                    object: ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String?) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            (getView() as IFormListrikReskinView).onSuccess(response, FavoriteType.removeFavorite)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    }
                }
            )
        }
    }

    override fun favoriteSavedList(param: SavedListNs) {
        getView().showProgress()
        val mUrl = GeneralHelper.getString(R.string.url_favorit_pln_v3)

        inquiryFavorite(param) {
            if (isViewAttached && mUrl != null) {
                val seqNum = brImoPrefRepository.seqNumber
                apiSource.getData(mUrl, param, seqNum).subscribeWithObserver(
                    compositeDisposable = compositeDisposable,
                    schedulerProvider = schedulerProvider,
                    createObserver = {
                        object: ApiObserver(view, seqNum) {
                            override fun onFailureHttp(errorMessage: String?) {
                                getView().hideProgress()
                                getView().onException(errorMessage)
                            }

                            override fun onApiCallSuccess(response: RestResponse) {
                                getView().hideProgress()
                                (getView() as IFormListrikReskinView).onSuccess(response, FavoriteType.favorite)
                            }

                            override fun onApiCallError(restResponse: RestResponse) {
                                getView().hideProgress()
                                getView().onException(restResponse.desc)
                            }
                        }
                    }
                )
            }
        }
    }

    override fun unfavoriteSavedList(param: SavedListNs) {
        getView().showProgress()
        val mUrl = GeneralHelper.getString(R.string.url_unfavorit_pln_v3)

        if (isViewAttached && mUrl != null) {
            val seqNum = brImoPrefRepository.seqNumber
            apiSource.getData(mUrl, param, seqNum).subscribeWithObserver(
                compositeDisposable = compositeDisposable,
                schedulerProvider = schedulerProvider,
                createObserver = {
                    object: ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String?) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            (getView() as IFormListrikReskinView).onSuccess(response, FavoriteType.unfavorite)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    }
                }
            )
        }
    }

    private fun inquiryFavorite(param: SavedListNs, listen: () -> Unit) {
        val mUrl = GeneralHelper.getString(R.string.url_inquiry_add_saved_list_listrik)
        val seqNum = brImoPrefRepository.seqNumber

        apiSource.getData(mUrl, param, seqNum).subscribeWithObserver(
            compositeDisposable = compositeDisposable,
            schedulerProvider = schedulerProvider,
            createObserver = {
                object: ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String?) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        listen.invoke()
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()
                        getView().onException(restResponse.desc)
                    }
                }
            }
        )
    }
}

enum class FavoriteType {
    favorite, unfavorite, removeFavorite
}