package id.co.bri.brimo.contract.IPresenter.dompetdigitalreskin

import id.co.bri.brimo.contract.IPresenter.base.IBaseFormRevampPresenter
import id.co.bri.brimo.contract.IView.base.IBaseFormRevampView
import id.co.bri.brimo.models.apimodel.response.EwalletProductListResponse

/**
 * Consolidated presenter interface that combines FormDompetDigitalRevamp and InputNomor functionality
 * for FormDompetDigitalReskinActivity which combines form, input, and wallet selection functionality
 */
interface IFormDompetDigitalReskinPresenter<V : IBaseFormRevampView> : IBaseFormRevampPresenter<V> {

    // Methods from IFormDompetDigitalRevPresenter
    fun getDataInquirySaved(isFromFastMenu: Boolean, walletCode: String, corpCode: String, purchaseNumber: String)

    // Methods from IInputNomorPresenter
    fun getDataInquiry(eWalletCode: String, corpCode: String, purchaseNumber: String)

    // Additional methods for wallet binding data (from DashboardIBPresenter)
    fun getEwalletBindingList()
    fun getEwalletBalance(ewalletProductListResponse: EwalletProductListResponse)
    fun setUrlEwalletBindingList(url: String)
    fun setUrlEwalletBalance(url: String)

    // Method to refresh data without showing loading indicator
    fun getDataFormSilent()
}