package id.co.bri.brimo.contract.IPresenter.simpedes;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;

public interface IDetailSimpedesPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrlPencairanForm(String urlPencairan);

    void setUrlTopupForm(String urlTopup);

    void setUrlPenutupan(String urlPenutupan);

    void getDataPencairanForm(String parentAccount, String childAccount);

    void getDataTopUpForm(String parentAccount, String childAccount);

    void getDataPenutupan(String parentAccount, String childAccount);
}
