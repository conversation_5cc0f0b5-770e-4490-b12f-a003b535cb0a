package id.co.bri.brimo.presenters.mutasi;

import androidx.annotation.Nullable;
import id.co.bri.brimo.contract.IPresenter.mutasi.IMutasiPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.mutasi.IMutasiView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.MutasiRequest;
import id.co.bri.brimo.models.apimodel.request.MutationDateRangeFilterRequest;
import id.co.bri.brimo.models.apimodel.request.MutationFilterRequest;
import id.co.bri.brimo.models.apimodel.request.MutationMonthFilterRequest;
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryFiveMutasiResponse;
import id.co.bri.brimo.models.apimodel.response.ListRekeningResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;

import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class MutasiPresenter<V extends IMvpView & IMutasiView> extends MvpPresenter<V> implements IMutasiPresenter<V> {

    private static final String TAG = "MutasiPresenter";

    protected String inquiryUrl;
    protected String formUrl;
    protected String urlRekening;
    protected String account;
    protected String urlGetMutationLastFive, urlGetMutationDateRange;

    ListRekeningResponse listRekeningResponse = new ListRekeningResponse();
    List<ListRekeningResponse.Account> accountList;
    Object request = null;

    public MutasiPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void onApiError(RestResponse restResponse) {
        getView().isHideSkeleton(true);
        getView().hideProgress();

        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_12.getValue())) {
            getView().onException12(restResponse.getDesc());
        } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
            getView().onException93(restResponse.getDesc());
        } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue())) {
            getView().onException99(restResponse.getDesc());
        } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_02.getValue())) {
            EmptyStateResponse emptyMutationResponse = restResponse.getData(EmptyStateResponse.class);
            getView().showEmptyState(emptyMutationResponse);
        } else
            super.onApiError(restResponse);
    }

    @Override
    public void getData() {
        if (formUrl == null || !isViewAttached()) {
            return;
        }

        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(formUrl, account, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.single())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                //TO-DO onSuccess
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    InquiryFiveMutasiResponse mutasiResponse = response.getData(InquiryFiveMutasiResponse.class);
                                    getView().onSuccessMutasiFive(mutasiResponse);
                                } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                    EmptyStateResponse emptyMutationResponse = response.getData(EmptyStateResponse.class);
                                    getView().showEmptyState(emptyMutationResponse);
                                }
                                getView().isHideSkeleton(true);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onApiError(restResponse);
                            }
                        })
        );
    }


    @Override
    public void getDataRekening() {
        if (!isViewAttached() || urlRekening == null)
            return;

        String seq = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable().add(
                getApiSource().getDataTanpaRequest(urlRekening, seq)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.single())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seq) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                listRekeningResponse = response.getData(ListRekeningResponse.class);
                                getView().onSuccessGetAccount(listRekeningResponse.getAccount());
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onApiError(restResponse);
                            }
                        })
        );

    }

    @Override
    public void getDataMutasi(MutasiRequest mutasiRequest) {
        if (inquiryUrl == null || !isViewAttached()) {
            return;
        }

        String seq = getBRImoPrefRepository().getSeqNumber();

        getView().showProgress();
        getCompositeDisposable().add(
                getApiSource().getData(inquiryUrl, mutasiRequest, seq)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seq) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().hideProgress();
                                //TO-DO onSuccess
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    InquiryFiveMutasiResponse mutasiResponse = response.getData(InquiryFiveMutasiResponse.class);
                                    getView().onSuccessMutasiFive(mutasiResponse);
                                } else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_01.getValue())) {
                                    EmptyStateResponse emptyMutationResponse = response.getData(EmptyStateResponse.class);
                                    getView().showEmptyState(emptyMutationResponse);
                                }
                                getView().isHideSkeleton(true);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                onApiError(restResponse);
                            }
                        })
        );
    }

    @Override
    public void setAccount(String account) {
        this.account = account;
    }

    @Override
    public void setInquiryUrl(String url) {
        this.inquiryUrl = url;
    }

    @Override
    public void setRekeningUrl(String url) {
        this.urlRekening = url;
    }

    @Override
    public void setFormUrl(String formUrl) {
        this.formUrl = formUrl;
    }

    @Override
    public void setUrlGetMutationLastFive(String url) {
        this.urlGetMutationLastFive = url;
    }

    @Override
    public void getMutationLastFive() {
        if (urlGetMutationLastFive == null || !isViewAttached()) {
            return;
        }
        getView().isHideSkeleton(false);
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable().add(
                getApiSource().getData(urlGetMutationLastFive, account, seqNum)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.single())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().isHideSkeleton(true);
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().isHideSkeleton(true);
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    InquiryFiveMutasiResponse mutasiResponse = response.getData(InquiryFiveMutasiResponse.class);
                                    getView().onSuccessMutasiFive(mutasiResponse);
                                } else if (restResponse.getCode().equalsIgnoreCase("NF") ||
                                        restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_02.getValue())) {
                                    EmptyStateResponse emptyMutationResponse = response.getData(EmptyStateResponse.class);
                                    getView().showEmptyState(emptyMutationResponse);
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().isHideSkeleton(true);
                                onApiError(restResponse);
                            }
                        })
        );
    }

    @Override
    public void setUrlGetMutationDateRange(String url) {
        this.urlGetMutationDateRange = url;
    }

    @Override
    public void getMutationRange(@Nullable MutationFilterRequest mutationFilterRequest, @Nullable MutationMonthFilterRequest mutationMonthFilterRequest,
                                 @Nullable MutationDateRangeFilterRequest mutationDateRangeFilterRequest) {
        if (urlGetMutationDateRange == null || !isViewAttached()) {
            return;
        }

        getView().isHideSkeleton(false);

        String seq = getBRImoPrefRepository().getSeqNumber();

        if (mutationFilterRequest != null) {
            request = mutationFilterRequest;
        } else if (mutationMonthFilterRequest != null) {
            request = mutationMonthFilterRequest;
        } else if (mutationDateRangeFilterRequest != null) {
            request = mutationDateRangeFilterRequest;
        }

        getCompositeDisposable().add(
                getApiSource().getData(urlGetMutationDateRange, request, seq)//function(param)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(), seq) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().isHideSkeleton(true);
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                getView().isHideSkeleton(true);
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                    InquiryFiveMutasiResponse mutasiResponse = response.getData(InquiryFiveMutasiResponse.class);
                                    getView().onSuccessGetMutation(mutasiResponse);
                                } else if (restResponse.getCode().equalsIgnoreCase("NF") ||
                                        restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_02.getValue())) {
                                    EmptyStateResponse emptyMutationResponse = response.getData(EmptyStateResponse.class);
                                    getView().showEmptyState(emptyMutationResponse);
                                }
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().isHideSkeleton(true);
                                onApiError(restResponse);
                            }
                        })
        );
    }
}