package id.co.bri.brimo.contract.IPresenter.fastmenunewskin

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.ValidateRequest
import id.co.bri.brimo.models.daomodel.FastMenuDefault

interface IFastMenuNewSkinPresenter<V : IMvpView?> : IMvpPresenter<V> {
    fun getInitiateResource()

    fun checkValidateBRIZZI()

    fun setUrlValidate(url: String?)

    fun setUrlSafetyMode(urlSafetyMode: String?)

    fun reValidateBRIZZI(validateRequest: ValidateRequest?)

    fun setUrlReadNotif(urlRead: String?)

    fun getReadNotifFastMenu(blastId: String?)

    fun getSafetyMode()

    val aktivasiVoiceAssistant: Boolean?

    fun updateChangeDeviceFlag(isChangeDevice: Boolean)

    val statusAktivasi: Boolean

    val bioChanged: Boolean

    val statusUpdateBio: Boolean

    fun updateStatusAktivasi(statusAktivasi: Boolean)

    fun updateBioChange(statusBioChange: Boolean)

    fun updateStatusUpdateBio(statusUpdate: Boolean)

    fun setUrlLogin(url: String?)

    fun setUrlLogout(url: String?)

    fun setUrlChange(url: String?)

    fun loginFingerprint()

    fun changeDevice(refNum: String?)

    val imageUrl: String?

    val bioType: String?

    fun getLocation(location: String?)

    //bilingual
    fun setUrlPrefrences(urlPrefrences: String?)

    fun updatePrefrencesLanguage(value: String?)

    fun getImageBannerUrl()

    val imageBannerUrlLocal: String?

    val bannerTitleLocal: String?

    fun getDefaultFastMenu()

    fun getSavedFastMenu()

    @JvmSuppressWildcards
    fun saveFirstFastMenuDefault(fastMenuDefaultList: List<FastMenuDefault>)

    fun getInitiateMenuRevamp(isToggled: Boolean, isAvailable: Boolean)

    val usernameAlias: String?

    fun getDataPayloadNfc(pin: String?)

    fun checkAvailabilityNfc(isNfcAvailable: Boolean)

    fun setUrlPromo(urlPromo: String)

    fun getPromo()

    fun getNameOfUser(): String

    fun setDetailItemPromoUrl(detailItemUrl: String?)

    fun getDetailPromoItem(id: String?)
}