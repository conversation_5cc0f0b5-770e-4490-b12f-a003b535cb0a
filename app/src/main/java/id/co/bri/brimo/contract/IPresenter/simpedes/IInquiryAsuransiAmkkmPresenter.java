package id.co.bri.brimo.contract.IPresenter.simpedes;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.base.IBaseInquiryView;
import id.co.bri.brimo.models.AccountModel;

public interface IInquiryAsuransiAmkkmPresenter<V extends IMvpView & IBaseInquiryView> extends IMvpPresenter<V> {

    void setUrlKonfirmasiAsuransiAmkkm(String url);

    void getKonfirmasi();

    void getSaldoSimpedes(AccountModel accountModel);
}
